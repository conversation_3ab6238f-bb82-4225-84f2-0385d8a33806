"""
Telugu Query Router Service

This service handles routing Telugu queries directly to the Telugu FAISS API
or falls back to translation-based processing.
"""

import logging
import requests
from typing import Dict, Any, Optional
from services.language_utils import enhanced_language_detector

# Configure logging
logger = logging.getLogger(__name__)

class TeluguQueryRouter:
    """
    Service to route Telugu queries directly to the Telugu FAISS API.
    """
    
    def __init__(self, telugu_api_base_url: str = "http://localhost:5000"):
        """
        Initialize the Telugu query router.

        Args:
            telugu_api_base_url: Base URL for the Telugu FAISS API
        """
        self.telugu_api_base_url = telugu_api_base_url.rstrip('/')
        self.telugu_endpoint = f"{self.telugu_api_base_url}/financial_query"
        logger.info(f"Telugu Query Router initialized with endpoint: {self.telugu_endpoint}")

    def should_route_to_telugu_api(self, query: str, data_language: Optional[str] = None) -> bool:
        """
        Determine if a query should be routed to the Telugu API.
        For now, we'll return False to use translation flow until Telugu-specific API is available.

        Args:
            query: User query text
            data_language: Language of the stored data (if known)

        Returns:
            bool: False (use translation flow for now)
        """
        # For now, always use translation flow since Telugu-specific API may not be available
        logger.info("Telugu router: Using translation flow (Telugu-specific API not yet available)")
        return False
    

    
    def route_telugu_query(self, query: str, additional_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Route a Telugu query to the Telugu FAISS API.
        
        Args:
            query: Telugu query text
            additional_params: Additional parameters to pass to the API
            
        Returns:
            Dict[str, Any]: Response from Telugu API or error information
        """
        try:
            # Prepare request payload
            payload = {
                "query": query,
                "language": "Telugu"
            }
            
            # Add any additional parameters
            if additional_params:
                payload.update(additional_params)
            
            logger.info(f"Sending Telugu query to API: {query[:50]}...")
            logger.debug(f"Full payload: {payload}")
            
            # Make request to Telugu API
            response = requests.post(
                self.telugu_endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Telugu API request successful")
                
                # Add metadata about routing
                result['routing_info'] = {
                    'routed_to_telugu_api': True,
                    'api_endpoint': self.telugu_endpoint,
                    'processing_type': 'direct_telugu_retrieval'
                }
                
                return result
            else:
                logger.error(f"Telugu API request failed with status {response.status_code}: {response.text}")
                return {
                    'error': f"Telugu API request failed with status {response.status_code}",
                    'error_type': 'telugu_api_error',
                    'routing_info': {
                        'routed_to_telugu_api': True,
                        'api_endpoint': self.telugu_endpoint,
                        'processing_type': 'direct_telugu_retrieval',
                        'error_details': response.text
                    }
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Telugu API request exception: {e}")
            return {
                'error': f"Telugu API connection error: {str(e)}",
                'error_type': 'telugu_api_connection_error',
                'routing_info': {
                    'routed_to_telugu_api': True,
                    'api_endpoint': self.telugu_endpoint,
                    'processing_type': 'direct_telugu_retrieval',
                    'connection_error': str(e)
                }
            }
        except Exception as e:
            logger.error(f"Unexpected error in Telugu routing: {e}")
            return {
                'error': f"Unexpected Telugu routing error: {str(e)}",
                'error_type': 'telugu_routing_error',
                'routing_info': {
                    'routed_to_telugu_api': True,
                    'api_endpoint': self.telugu_endpoint,
                    'processing_type': 'direct_telugu_retrieval',
                    'unexpected_error': str(e)
                }
            }

# Create a global instance for easy import
telugu_router = TeluguQueryRouter()
