#!/usr/bin/env python3
"""
Simple test for translation providers
"""

import requests
import json

def test_mymemory_direct():
    """Test MyMemory API directly"""
    print("🌐 Testing MyMemory API directly")
    print("=" * 40)
    
    test_cases = [
        ('Hello, how are you?', 'en', 'te', 'Telugu'),
        ('Hello, how are you?', 'en', 'kn', 'Kannada'),
        ('Hello, how are you?', 'en', 'or', 'Oriya'),
        ('Hello, how are you?', 'en', 'ta', 'Tamil')
    ]
    
    for text, source, target, lang_name in test_cases:
        print(f"\n📝 Testing English → {lang_name}")
        print(f"Original: {text}")
        
        try:
            url = "https://api.mymemory.translated.net/get"
            params = {
                'q': text,
                'langpair': f"{source}|{target}"
            }
            
            response = requests.get(url, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('responseStatus') == 200:
                    translated = data.get('responseData', {}).get('translatedText', '')
                    if translated and translated != text:
                        print(f"✅ MyMemory Success: {translated}")
                    else:
                        print("❌ MyMemory returned empty or same text")
                else:
                    print(f"❌ MyMemory API error: {data.get('responseDetails', 'Unknown error')}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")

def test_libretranslate_direct():
    """Test LibreTranslate API directly"""
    print("\n🌐 Testing LibreTranslate API directly")
    print("=" * 40)
    
    # List of LibreTranslate instances
    libretranslate_urls = [
        "https://libretranslate.de/translate",
        "https://translate.argosopentech.com/translate",
        "https://libretranslate.com/translate"
    ]
    
    test_cases = [
        ('Hello, how are you?', 'en', 'te', 'Telugu'),
        ('Hello, how are you?', 'en', 'kn', 'Kannada'),
        ('Hello, how are you?', 'en', 'hi', 'Hindi'),  # Use Hindi instead of Oriya as it's more supported
        ('Hello, how are you?', 'en', 'ta', 'Tamil')
    ]
    
    for text, source, target, lang_name in test_cases:
        print(f"\n📝 Testing English → {lang_name}")
        print(f"Original: {text}")
        
        success = False
        for url in libretranslate_urls:
            if success:
                break
                
            try:
                data = {
                    'q': text,
                    'source': source,
                    'target': target,
                    'format': 'text'
                }
                
                headers = {'Content-Type': 'application/json'}
                response = requests.post(url, json=data, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    translated = result.get('translatedText', '')
                    if translated and translated != text:
                        print(f"✅ LibreTranslate Success ({url}): {translated}")
                        success = True
                    else:
                        print(f"⚠️ LibreTranslate ({url}) returned empty or same text")
                else:
                    print(f"⚠️ LibreTranslate ({url}) HTTP Error: {response.status_code}")
                    
            except Exception as e:
                print(f"⚠️ LibreTranslate ({url}) Exception: {e}")
        
        if not success:
            print(f"❌ All LibreTranslate instances failed for {lang_name}")

def test_comprehensive_translation():
    """Test the comprehensive translation method"""
    print("\n🔄 Testing Comprehensive Translation Method")
    print("=" * 40)
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from services.translation_service import TranslationService
        
        translation_service = TranslationService()
        
        test_cases = [
            ('Hello, how are you?', 'en', 'te', 'Telugu'),
            ('Hello, how are you?', 'en', 'kn', 'Kannada'),
            ('Hello, how are you?', 'en', 'or', 'Oriya'),
            ('Hello, how are you?', 'en', 'ta', 'Tamil')
        ]
        
        for text, source, target, lang_name in test_cases:
            print(f"\n📝 Testing English → {lang_name} (Comprehensive)")
            print(f"Original: {text}")
            
            try:
                result = translation_service._attempt_translation(text, source, target)
                
                if result.get('success') and result.get('translated_text'):
                    translated = result['translated_text']
                    provider = result.get('provider', 'unknown')
                    print(f"✅ Comprehensive Success ({provider}): {translated}")
                else:
                    print("❌ Comprehensive translation failed")
                    
            except Exception as e:
                print(f"❌ Comprehensive Exception: {e}")
                
    except ImportError as e:
        print(f"❌ Cannot import translation service: {e}")
    except Exception as e:
        print(f"❌ Comprehensive test failed: {e}")

def main():
    """Run all translation tests"""
    print("🚀 Testing Translation Providers")
    print("=" * 60)
    
    # Test direct APIs first
    test_mymemory_direct()
    test_libretranslate_direct()
    
    # Test comprehensive method
    test_comprehensive_translation()
    
    print("\n📊 Test Summary")
    print("=" * 60)
    print("If any provider shows success, the backup translation system should work.")
    print("The comprehensive method should use the working providers automatically.")

if __name__ == '__main__':
    main()
