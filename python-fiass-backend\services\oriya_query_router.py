"""
Oriya Query Router Service

This service handles routing Oriya queries directly to the Oriya FAISS API
or falls back to translation-based processing.
"""

import logging
import requests
from typing import Dict, Any, Optional
from services.language_utils import enhanced_language_detector

# Configure logging
logger = logging.getLogger(__name__)

class OriyaQueryRouter:
    """
    Service to route Oriya queries directly to the Oriya FAISS API.
    """
    
    def __init__(self, oriya_api_base_url: str = "http://localhost:5000"):
        """
        Initialize the Oriya query router.
        
        Args:
            oriya_api_base_url: Base URL for the Oriya FAISS API
        """
        self.oriya_api_base_url = oriya_api_base_url.rstrip('/')
        self.oriya_endpoint = f"{self.oriya_api_base_url}/financial_query"
        logger.info(f"Oriya Query Router initialized with endpoint: {self.oriya_endpoint}")
    
    def should_route_to_oriya_api(self, query: str, data_language: Optional[str] = None) -> bool:
        """
        Determine if a query should be routed to the Oriya API.
        For now, we'll return False to use translation flow until Oriya-specific API is available.

        Args:
            query: User query text
            data_language: Language of the stored data (if known)

        Returns:
            bool: False (use translation flow for now)
        """
        # For now, always use translation flow since Oriya-specific API may not be available
        logger.info("Oriya router: Using translation flow (Oriya-specific API not yet available)")
        return False
    
    def route_oriya_query(self, query: str, additional_params: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Route an Oriya query to the Oriya FAISS API.
        
        Args:
            query: Oriya query text
            additional_params: Additional parameters to pass to the API
            
        Returns:
            Dict[str, Any]: Response from Oriya API or error information
        """
        try:
            # Prepare request payload
            payload = {
                "query": query,
                "language": "Oriya"
            }
            
            # Add any additional parameters
            if additional_params:
                payload.update(additional_params)
            
            logger.info(f"Sending Oriya query to API: {query[:50]}...")
            logger.debug(f"Full payload: {payload}")
            
            # Make request to Oriya API
            response = requests.post(
                self.oriya_endpoint,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info("Oriya API request successful")
                
                # Add metadata about routing
                result['routing_info'] = {
                    'routed_to_oriya_api': True,
                    'api_endpoint': self.oriya_endpoint,
                    'processing_type': 'direct_oriya_retrieval'
                }
                
                return result
            else:
                logger.error(f"Oriya API request failed with status {response.status_code}: {response.text}")
                return {
                    'error': f"Oriya API request failed with status {response.status_code}",
                    'error_type': 'oriya_api_error',
                    'routing_info': {
                        'routed_to_oriya_api': True,
                        'api_endpoint': self.oriya_endpoint,
                        'processing_type': 'direct_oriya_retrieval',
                        'error_details': response.text
                    }
                }
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Oriya API request exception: {e}")
            return {
                'error': f"Oriya API connection error: {str(e)}",
                'error_type': 'oriya_api_connection_error',
                'routing_info': {
                    'routed_to_oriya_api': True,
                    'api_endpoint': self.oriya_endpoint,
                    'processing_type': 'direct_oriya_retrieval',
                    'connection_error': str(e)
                }
            }
        except Exception as e:
            logger.error(f"Unexpected error in Oriya routing: {e}")
            return {
                'error': f"Unexpected Oriya routing error: {str(e)}",
                'error_type': 'oriya_routing_error',
                'routing_info': {
                    'routed_to_oriya_api': True,
                    'api_endpoint': self.oriya_endpoint,
                    'processing_type': 'direct_oriya_retrieval',
                    'unexpected_error': str(e)
                }
            }

# Create a global instance for easy import
oriya_router = OriyaQueryRouter()
